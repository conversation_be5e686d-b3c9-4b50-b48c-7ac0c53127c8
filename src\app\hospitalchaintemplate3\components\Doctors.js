"use client";

import { Box, Skeleton } from "@mui/material";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation } from "swiper/modules";
import Doctor<PERSON><PERSON> from "./DoctorCard";

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/autoplay";

const Doctors = ({ doctors = [], isLoading = false, locationCode, swiperRef, selectedLocation = null }) => {

  if (isLoading) {
    return (
      <Box sx={{ display: "flex", gap: "16px", overflow: "hidden" }}>
        {[1, 2, 3, 4].map((_, index) => (
          <Skeleton
            key={index}
            variant="rounded"
            height={350}
            sx={{
              width: "100%",
              minWidth: { xs: "280px", sm: "240px", md: "220px" },
              borderRadius: "16px"
            }}
          />
        ))}
      </Box>
    );
  }

  return (
    <Box > {/* Allow cards to be fully visible */}
      <Swiper
        ref={swiperRef}
        breakpoints={{
          0: {
            slidesPerView: 1,
            spaceBetween: 10,
          },
          600: {
            slidesPerView: 2,
            spaceBetween: 10,
          },
          1000: {
            slidesPerView: 3,
            spaceBetween: 10,
          },
          1200: {
            slidesPerView: 3,
            spaceBetween: 10,
          },
          1400: {
            slidesPerView: 5,
            spaceBetween: 300,
          },
        }}
        modules={[Autoplay, Navigation]}
        autoplay={{
          delay: 4000,
          disableOnInteraction: false,
        }}
        loop={true}
        navigation={false}
        className="doctorsSwiper"
        style={{
          xs:{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          },
          paddingBottom: "20px", // Increased padding to prevent card cutoff
        }}
      >
        {doctors.map((doctor, index) => {
          const {
            code = null,
            doctorDetails = {},
          } = doctor || {};

          return (
            <SwiperSlide key={code || index}>
              <DoctorCard
                doctorDetails={doctorDetails}
                locationCode={locationCode}
                selectedLocation={selectedLocation}
              />
            </SwiperSlide>
          );
        })}
      </Swiper>
    </Box>
  );
};

export default Doctors;
