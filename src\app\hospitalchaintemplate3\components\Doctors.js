"use client";

import { Box, Skeleton } from "@mui/material";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation } from "swiper/modules";
import DoctorC<PERSON> from "./DoctorCard";

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/autoplay";

const Doctors = ({ doctors = [], isLoading = false, locationCode, swiperRef, selectedLocation = null }) => {

  if (isLoading) {
    return (
      <Box sx={{ display: "flex", gap: "16px", overflow: "hidden" }}>
        {[1, 2, 3, 4].map((_, index) => (
          <Skeleton
            key={index}
            variant="rounded"
            height={350}
            sx={{
              width: "100%",
              minWidth: { xs: "280px", sm: "240px", md: "220px" },
              borderRadius: "16px"
            }}
          />
        ))}
      </Box>
    );
  }

  return (
    <Box sx={{ overflow: "visible" }}> {/* Allow cards to be fully visible */}
      <Swiper
        ref={swiperRef}
        spaceBetween={16}
        breakpoints={{
          0: {
            slidesPerView: 1,
          },
          600: {
            slidesPerView: 2,
          },
          900: {
            slidesPerView: 3,
          },
          1200: {
            slidesPerView: 4,
          },
          1400: {
            slidesPerView: 5,
          },
        }}
        modules={[Autoplay, Navigation]}
        autoplay={{
          delay: 4000,
          disableOnInteraction: false,
        }}
        loop={true}
        navigation={false}
        className="doctorsSwiper"
        style={{
          paddingBottom: "20px", // Increased padding to prevent card cutoff
          overflow: "visible", // Ensure cards are fully visible
        }}
      >
        {doctors.map((doctor, index) => {
          const {
            code = null,
            doctorDetails = {},
          } = doctor || {};

          return (
            <SwiperSlide key={code || index}>
              <DoctorCard
                doctorDetails={doctorDetails}
                locationCode={locationCode}
                selectedLocation={selectedLocation}
              />
            </SwiperSlide>
          );
        })}
      </Swiper>
    </Box>
  );
};

export default Doctors;
