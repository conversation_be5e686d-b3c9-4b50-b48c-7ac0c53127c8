"use client";

import { Box, Skeleton } from "@mui/material";
import { useRef } from "react";
import { useRouter } from "next/navigation";
import { useTheme } from "@emotion/react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation } from "swiper/modules";
import Doctor<PERSON><PERSON> from "./DoctorCard";

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/autoplay";

const Doctors = ({ doctors = [], isLoading = false, locationCode, swiperRef }) => {
  const router = useRouter();
  const theme = useTheme();

  if (isLoading) {
    return (
      <Box sx={{ display: "flex", gap: "24px", overflow: "hidden" }}>
        {[1, 2, 3].map((_, index) => (
          <Skeleton
            key={index}
            variant="rounded"
            height={400}
            sx={{ 
              width: "100%", 
              minWidth: { xs: "280px", sm: "320px", md: "350px" },
              borderRadius: "12px"
            }}
          />
        ))}
      </Box>
    );
  }

  return (
    <Box sx={{ overflow: "hidden" }}> {/* Prevent overflow */}
      <Swiper
        ref={swiperRef}
        spaceBetween={24}
        breakpoints={{
          0: {
            slidesPerView: 1,
          },
          600: {
            slidesPerView: 2,
          },
          900: {
            slidesPerView: 2.5,
          },
          1200: {
            slidesPerView: 3,
          },
        }}
        modules={[Autoplay, Navigation]}
        autoplay={{
          delay: 4000,
          disableOnInteraction: false,
        }}
        loop={true}
        navigation={false}
        className="doctorsSwiper"
        style={{
          paddingBottom: "8px", // Small padding to prevent shadow cutoff
        }}
      >
        {doctors.map((doctor, index) => {
          const {
            code = null,
            doctorDetails = {},
          } = doctor || {};

          return (
            <SwiperSlide key={code || index}>
              <DoctorCard
                doctorDetails={doctorDetails}
                locationCode={locationCode}
              />
            </SwiperSlide>
          );
        })}
      </Swiper>
    </Box>
  );
};

export default Doctors;
