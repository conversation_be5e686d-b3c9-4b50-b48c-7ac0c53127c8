"use client";

import { alpha, Box, Typography, IconButton } from "@mui/material";
import { useContext, useEffect, useState, useRef } from "react";
import { AppContext } from "../../AppContextLayout";
import { useRouter } from "next/navigation";
import { useTheme } from "@emotion/react";
import Doctors from "./Doctors";
import { ArrowRightAlt, ArrowBackIos, ArrowForwardIos } from "@mui/icons-material";
import { getHomeSectionHeadings, getDoctors } from "@/api/harbor.service";
import { getWebsiteHost } from "@/app/utils/clientOnly/clientUtils";
import { HOME_SECTION_HEADING_TYPE } from "@/constants";

const DoctorsWrapper = () => {
  const [doctors, setDoctors] = useState([]);
  const [headings, setHeadings] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const { websiteData = {} } = useContext(AppContext);
  const { enterprise_code: enterpriseCode = null, location_code: locationCode = null } =
    websiteData || {};
  const router = useRouter();
  const theme = useTheme();
  const swiperRef = useRef(null);

  const fetchDoctors = async () => {
    setIsLoading(true);
    try {
      const response = await getDoctors(enterpriseCode);
      const { result = {} } = response || {};
      const { doctors = [] } = result || {};
      setDoctors(doctors);
    } catch (error) {
      console.error("Error fetching doctors:", error);
      // setViewSnackbarMain({ message: "Something went wrong!", type: "error" });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchDoctorsHeadings = async () => {
    try {
      const domainName = getWebsiteHost();
      const data = await getHomeSectionHeadings(
        { domainName: domainName },
        HOME_SECTION_HEADING_TYPE.DOCTORS
      );
      if (data?.code === 200) {
        setHeadings(data?.result || []);
      }
    } catch (error) {
      console.error("Error fetching doctors headings:", error);
    }
  };

  useEffect(() => {
    if (enterpriseCode) fetchDoctors();
    fetchDoctorsHeadings();
  }, [enterpriseCode]);

  if (!doctors.length && !isLoading) {
    return null;
  }

  const handleViewAllClick = () => {
    router.push(`/doctors`);
  };

  const handlePrevSlide = () => {
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slidePrev();
    }
  };

  const handleNextSlide = () => {
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slideNext();
    }
  };

  return (
    <Box
      sx={{
        padding: { xs: "40px 16px", sm: "60px 24px", md: "80px 100px" },
        backgroundColor: "#fff",
      }}
    >
      <Box sx={{ maxWidth: "1400px", margin: "0 auto" }}>
        {/* Header Section */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: { xs: "flex-start", sm: "flex-end" },
            marginBottom: "40px",
            flexDirection: { xs: "column", sm: "row" },
            gap: { xs: "16px", sm: "24px" },
          }}
        >
          <Box sx={{ flex: 1 }}>
            <Typography
              variant="h2"
              sx={{
                fontSize: { xs: "28px", sm: "32px", md: "36px" },
                fontWeight: 600,
                color: "#1a1a1a",
                marginBottom: "8px",
                lineHeight: 1.2,
              }}
            >
              {headings[0]?.heading || "Our Doctors"}
            </Typography>
            <Typography
              variant="body1"
              sx={{
                color: "rgba(0, 0, 0, 0.6)",
                fontSize: "14px",
                maxWidth: "600px",
                lineHeight: 1.5,
              }}
            >
              {headings[0]?.subHeading || "Meet our experienced team of medical professionals dedicated to providing exceptional healthcare."}
            </Typography>
          </Box>
          <Typography
            variant="body2"
            sx={{
              color: theme.palette.primary.main,
              cursor: "pointer",
              display: "flex",
              alignItems: "center",
              gap: "4px",
              fontSize: "16px",
              fontWeight: 500,
              alignSelf: { xs: "flex-start", sm: "auto" }, // Align to start on mobile
            }}
            onClick={handleViewAllClick}
          >
            View All <ArrowRightAlt />
          </Typography>
        </Box>

        <Doctors
          doctors={doctors}
          isLoading={isLoading}
          locationCode={locationCode}
          swiperRef={swiperRef}
        />

        {/* Navigation Buttons */}
        {!isLoading && doctors.length > 0 && (
          <Box
            sx={{
              display: "flex",
              gap: "12px",
              marginTop: "32px",
              justifyContent: "flex-start",
            }}
          >
            <IconButton
              onClick={handlePrevSlide}
              sx={{
                width: "48px",
                height: "48px",
                border: `2px solid ${theme.palette.primary.main}`,
                borderRadius: "8px",
                color: theme.palette.primary.main,
                backgroundColor: "transparent",
                "&:hover": {
                  backgroundColor: theme.palette.primary.main,
                  color: "#fff",
                },
                transition: "all 0.3s ease",
              }}
            >
              <ArrowBackIos sx={{ fontSize: "20px" }} />
            </IconButton>
            <IconButton
              onClick={handleNextSlide}
              sx={{
                width: "48px",
                height: "48px",
                border: `2px solid ${theme.palette.primary.main}`,
                borderRadius: "8px",
                color: theme.palette.primary.main,
                backgroundColor: "transparent",
                "&:hover": {
                  backgroundColor: theme.palette.primary.main,
                  color: "#fff",
                },
                transition: "all 0.3s ease",
              }}
            >
              <ArrowForwardIos sx={{ fontSize: "20px" }} />
            </IconButton>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default DoctorsWrapper;
