"use client";

import { CurrencyRupee, Work } from "@mui/icons-material";
import {
  Box,
  Typography,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
  Button,
  Slide
} from "@mui/material";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useTheme } from "@emotion/react";
import { getThumborUrl } from "@/app/utils/getThumborUrl";
import { forwardRef, useContext, useEffect, useState } from "react";
import { AppContext } from "@/app/AppContextLayout";

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const DoctorCard = ({
  doctorDetails = {},
  locationCode = null,
  selectedLocation = null,
}) => {
  const router = useRouter();
  const theme = useTheme();
  const [open, setOpen] = useState(false);
  const [selectedCenter, setSelectedCenter] = useState(null);
  const { websiteData = {} } = useContext(AppContext);
  const { enterprise_code: chainEnterpriseCode = null } = websiteData || {};

  const {
    code: doctorCode = null,
    name = "",
    profilePicture = "",
    medicalSpecialities = [],
    additionalDetails = null,
    educationDetails = [],
    seoSlug = "",
    centers = [],
  } = doctorDetails || {};

  const { workExperience = "", consultationFee = "" } = additionalDetails || {};
  const { domain_slug: centerSlug = "" } = selectedCenter || {};

  // Get the first speciality for display
  const specialization = medicalSpecialities[0]?.displayName || "";

  const handleDoctorClick = () => {
    // If location is already selected, go directly
    if (locationCode || (selectedLocation && selectedLocation !== chainEnterpriseCode)) {
      router.push(`/doctors/${locationCode || centerSlug}/${seoSlug}`);
    }
    // Multiple centers - show modal to choose
    else if (centers.length > 1) {
      setOpen(true);
    }
    // Single center - go directly to that center
    else if (centers.length === 1) {
      router.push(`/doctors/${centers[0].domain_slug}/${seoSlug}`);
    }
    // No centers - fallback route
    else {
      router.push(`/doctors/${seoSlug || doctorCode}`);
    }
  };

  const handleCentersModal = () => setOpen(false);

  const handleCenterChange = (event) => {
    const selectedValue = centers.find(
      (center) => center.code === event.target.value
    );
    setSelectedCenter(selectedValue);
  };

  const handleRedirection = () => {
    router.push(`/doctors/${centerSlug}/${seoSlug}`);
  };

  useEffect(() => {
    if (centers.length > 0) {
      setSelectedCenter(centers[0]);
    }
  }, [centers]);

  useEffect(() => {
    if (selectedLocation) {
      const selectedValue = centers.find(
        (center) => center.code === selectedLocation
      );
      setSelectedCenter(selectedValue);
    }
  }, [selectedLocation]);

  return (
    <>
    <Box
        onClick={handleDoctorClick}
          sx={{
            cursor: "pointer",
            width: 280, // ID card-like proportions (smaller width)
            height: 420,
            borderRadius: "20px",
            padding: "10px 10px 0 10px",
            backgroundColor: "#fff",
            boxShadow: "0 1px 5px rgba(0, 0, 0, 0.1)",
            transition: "all 0.3s ease", // Smooth transition for all properties
            "&:hover": {
              boxShadow: "0 4px 8px rgba(0, 0, 0, 0.12)",
              transform: "translateY(-2px)",
            },
          }}
        >
          <Box
            sx={{
              position: "relative",
              width: "100%",
              height: 260,
              borderRadius: "20px",
              overflow: "hidden",
              marginBottom: "8px",
            }}
          >
            <Image
              src={profilePicture || "/doctor-profile-icon.png"}
              alt={name}
              fill
              style={{
                objectFit: "cover",
                borderRadius: "20px",
              }}
              priority
            />
          </Box>
    
          <Box sx={{
              textAlign: "center",
              padding: "8px 0 16px",
            }}>
            <Typography
              variant="h3"
              sx={{
                mb: 0.5,
                color: "#1a1a1a",
                fontSize: "1.125rem",
              }}
            >
              {name}
            </Typography>
    
            <Typography
              variant="body2"
              sx={{
                color: "rgb(75, 75, 75)",
                mb: 1.5,
                display: "-webkit-box",
                height: "40px",
                WebkitLineClamp: 2,
                WebkitBoxOrient: "vertical",
                overflow: "hidden",
                textOverflow: "ellipsis",
              }}
            >
              {specialization}
            </Typography>
    
            <Box
              sx={{
                width:"fit",
                display: "inline-flex",
                alignItems:"center",
                gap:5,
                p: 1,
                // mb: 1,
                // backgroundColor: "grey.50",
                borderRadius: "12px",
              }}
            >
              {workExperience ? <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Work sx={{ color: "primary.main", fontSize: 20 }} />
                <Box>
                  <Typography
                    variant="caption"
                    sx={{ color: "rgb(75, 75, 75)", display: "block" }}
                  >
                    Experience
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{ fontWeight: 600, color: "#1a1a1a" }}
                  >
                    {workExperience} Years
                  </Typography>
                </Box>
              </Box> : ""}
              
    
              {consultationFee ? <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <CurrencyRupee sx={{ color: "primary.main", fontSize: 20 }} />
                <Box>
                  <Typography
                    variant="caption"
                    sx={{ color: "rgb(75, 75, 75)", display: "block" }}
                  >
                    Consultation
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{ fontWeight: 600, color: "#1a1a1a" }}
                  >
                    ₹{consultationFee }
                    
                  </Typography>
                </Box>
              </Box> : ""}
            </Box>
    
            {/* <Box
              sx={{
                textAlign: "center",
              }}
            >
              <Button
              onClick={handleDoctorClick}
                color="primary"
                sx={{
                  borderRadius: "100px",
                  backgroundColor: "primary.main",
                  transform: "scale(1)",
                  color: "text.primary",
                  textTransform: "capitalize",
                  fontWeight: "normal",
                  fontSize: "14px",
                  padding: "10px 20px",
                  transition: "transform 0.3s ease",
                  "&:hover": {
                    backgroundColor: "primary.main",
                  },
                }}
              >
                Book Appointment
              </Button>
            </Box> */}
          </Box>
        </Box>

    {/* Location Selection Dialog */}
    <Dialog
      open={open}
      TransitionComponent={Transition}
      onClose={handleCentersModal}
      keepMounted={false}
      aria-describedby="alert-dialog-slide-description"
      sx={{ ".MuiDialog-paper": { width: { xs: "100%", sm: "600px" } } }}
    >
      <DialogTitle>Select Location</DialogTitle>
      <DialogContent>
        <FormControl sx={{ padding: "8px 16px" }}>
          <RadioGroup
            aria-labelledby="demo-radio-buttons-group-label"
            name="radio-buttons-group"
            value={selectedCenter?.code}
            onChange={handleCenterChange}
          >
            {centers.map((center) => {
              const { code = null, name = "", area = {} } = center || {};
              const { name: areaName = "" } = area || {};
              return (
                <FormControlLabel
                  key={code}
                  value={code}
                  control={<Radio />}
                  label={`${name || ""} - ${areaName}`}
                />
              );
            })}
          </RadioGroup>
        </FormControl>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleCentersModal} sx={{ color: theme.palette.primary.main }}>
          Close
        </Button>
        <Button
          onClick={handleRedirection}
          variant="contained"
          sx={{ backgroundColor: theme.palette.primary.main }}
        >
          Confirm
        </Button>
      </DialogActions>
    </Dialog>
    </>
  );
};

export default DoctorCard;
