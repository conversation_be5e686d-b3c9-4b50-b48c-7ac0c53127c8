"use client";

import { CurrencyRupee, Work } from "@mui/icons-material";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useTheme } from "@emotion/react";
import { getThumborUrl } from "@/app/utils/getThumborUrl";

const DoctorCard = ({
  doctorDetails = {},
  locationCode = null,
}) => {
  const router = useRouter();
  const theme = useTheme();

  const {
    code: doctorCode = null,
    name = "",
    profilePicture = "",
    medicalSpecialities = [],
    additionalDetails = null,
    educationDetails = [],
    seoSlug = "",
  } = doctorDetails || {};

  const { workExperience = "", consultationFee = "" } = additionalDetails || {};
  
  // Get the first speciality for display
  const specialization = medicalSpecialities[0]?.displayName || "";

  const handleDoctorClick = () => {
    if (locationCode) {
      router.push(`/doctors/${locationCode}/${seoSlug || doctorCode}`);
    } else {
      router.push(`/doctors/${seoSlug || doctorCode}`);
    }
  };

  return (
    <Box
      onClick={handleDoctorClick}
      sx={{
        cursor: "pointer",
        height: "400px",
        width: "100%",
        borderRadius: "12px",
        backgroundColor: "#fff",
        boxShadow: "0 2px 12px rgba(0, 0, 0, 0.08)",
        transition: "all 0.3s ease",
        overflow: "hidden",
        "&:hover": {
          transform: "translateY(-4px)",
          boxShadow: "0 8px 25px rgba(0, 0, 0, 0.15)",
        },
      }}
    >
      {/* Image Section */}
      <Box
        sx={{
          position: "relative",
          width: "100%",
          height: "240px",
          overflow: "hidden",
        }}
      >
        <Image
          src={profilePicture ? getThumborUrl(profilePicture, 350, 240) : "/doctor-profile-icon.png"}
          alt={name}
          fill
          style={{
            objectFit: "cover",
          }}
          priority
        />
      </Box>

      {/* Content Section */}
      <Box sx={{
        padding: "20px 16px",
        display: "flex",
        flexDirection: "column",
        gap: "12px",
        height: "160px",
      }}>
        {/* Doctor Name */}
        <Typography
          variant="h6"
          sx={{
            color: "#1a1a1a",
            fontSize: "18px",
            fontWeight: 600,
            lineHeight: 1.3,
            overflow: "hidden",
            textOverflow: "ellipsis",
            display: "-webkit-box",
            WebkitLineClamp: 1,
            WebkitBoxOrient: "vertical",
          }}
        >
          {name}
        </Typography>

        {/* Specialization */}
        <Typography
          variant="body2"
          sx={{
            color: "rgba(0, 0, 0, 0.6)",
            fontSize: "14px",
            lineHeight: 1.4,
            overflow: "hidden",
            textOverflow: "ellipsis",
            display: "-webkit-box",
            WebkitLineClamp: 2,
            WebkitBoxOrient: "vertical",
            minHeight: "40px",
          }}
        >
          {specialization}
        </Typography>

        {/* Experience and Fee */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            gap: "16px",
            mt: "auto",
          }}
        >
          {workExperience && (
            <Box sx={{ display: "flex", alignItems: "center", gap: "6px", flex: 1 }}>
              <Work sx={{ color: theme.palette.primary.main, fontSize: 18 }} />
              <Box>
                <Typography
                  variant="caption"
                  sx={{ color: "rgba(0, 0, 0, 0.5)", display: "block", fontSize: "11px" }}
                >
                  Experience
                </Typography>
                <Typography
                  variant="body2"
                  sx={{ fontWeight: 600, color: "#1a1a1a", fontSize: "13px" }}
                >
                  {workExperience} Years
                </Typography>
              </Box>
            </Box>
          )}

          {consultationFee && (
            <Box sx={{ display: "flex", alignItems: "center", gap: "6px", flex: 1 }}>
              <CurrencyRupee sx={{ color: theme.palette.primary.main, fontSize: 18 }} />
              <Box>
                <Typography
                  variant="caption"
                  sx={{ color: "rgba(0, 0, 0, 0.5)", display: "block", fontSize: "11px" }}
                >
                  Consultation
                </Typography>
                <Typography
                  variant="body2"
                  sx={{ fontWeight: 600, color: "#1a1a1a", fontSize: "13px" }}
                >
                  ₹{consultationFee}
                </Typography>
              </Box>
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default DoctorCard;
