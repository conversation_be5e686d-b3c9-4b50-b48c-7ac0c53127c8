"use client";

import { CurrencyRupee, Work } from "@mui/icons-material";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useTheme } from "@emotion/react";
import { getThumborUrl } from "@/app/utils/getThumborUrl";

const DoctorCard = ({
  doctorDetails = {},
  locationCode = null,
}) => {
  const router = useRouter();
  const theme = useTheme();

  const {
    code: doctorCode = null,
    name = "",
    profilePicture = "",
    medicalSpecialities = [],
    additionalDetails = null,
    educationDetails = [],
    seoSlug = "",
  } = doctorDetails || {};

  const { workExperience = "", consultationFee = "" } = additionalDetails || {};
  
  // Get the first speciality for display
  const specialization = medicalSpecialities[0]?.displayName || "";

  const handleDoctorClick = () => {
    if (locationCode) {
      router.push(`/doctors/${locationCode}/${seoSlug || doctorCode}`);
    } else {
      router.push(`/doctors/${seoSlug || doctorCode}`);
    }
  };

  return (
    <Box
        onClick={handleDoctorClick}
          sx={{
            cursor: "pointer",
            maxWidth: 280,
            height: 420,
            width: "100%",
            borderRadius: "20px",
            padding: "10px 10px 0 10px",
            backgroundColor: "#fff",
            boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)", 
            "&:hover": {
              transform: "scale(1.03)", 
              boxShadow: "0 6px 12px rgba(0, 0, 0, 0.15)",
            },
          }}
        >
          <Box
            sx={{
              position: "relative",
              width: "100%",
              height: 260,
              borderRadius: "20px",
              overflow: "hidden",
              marginBottom: "8px",
            }}
          >
            <Image
              src={profilePicture || "/doctor-profile-icon.png"}
              alt={name}
              fill
              style={{
                objectFit: "cover",
                borderRadius: "20px",
              }}
              priority
            />
          </Box>
    
          <Box sx={{
              textAlign: "center",
              padding: "8px 0 16px",
            }}>
            <Typography
              variant="h3"
              sx={{
                mb: 0.5,
                color: "#1a1a1a",
                fontSize: "1.125rem",
              }}
            >
              {name}
            </Typography>
    
            <Typography
              variant="body2"
              sx={{
                color: "rgb(75, 75, 75)",
                mb: 1.5,
                display: "-webkit-box",
                height: "40px",
                WebkitLineClamp: 2,
                WebkitBoxOrient: "vertical",
                overflow: "hidden",
                textOverflow: "ellipsis",
              }}
            >
              {specialization}
            </Typography>
    
            <Box
              sx={{
                width:"fit",
                display: "inline-flex",
                alignItems:"center",
                gap:5,
                p: 1,
                // mb: 1,
                // backgroundColor: "grey.50",
                borderRadius: "12px",
              }}
            >
              {workExperience ? <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Work sx={{ color: "primary.main", fontSize: 20 }} />
                <Box>
                  <Typography
                    variant="caption"
                    sx={{ color: "rgb(75, 75, 75)", display: "block" }}
                  >
                    Experience
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{ fontWeight: 600, color: "#1a1a1a" }}
                  >
                    {workExperience} Years
                  </Typography>
                </Box>
              </Box> : ""}
              
    
              {consultationFee ? <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <CurrencyRupee sx={{ color: "primary.main", fontSize: 20 }} />
                <Box>
                  <Typography
                    variant="caption"
                    sx={{ color: "rgb(75, 75, 75)", display: "block" }}
                  >
                    Consultation
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{ fontWeight: 600, color: "#1a1a1a" }}
                  >
                    ₹{consultationFee }
                    
                  </Typography>
                </Box>
              </Box> : ""}
            </Box>
    
            {/* <Box
              sx={{
                textAlign: "center",
              }}
            >
              <Button
              onClick={handleDoctorClick}
                color="primary"
                sx={{
                  borderRadius: "100px",
                  backgroundColor: "primary.main",
                  transform: "scale(1)",
                  color: "text.primary",
                  textTransform: "capitalize",
                  fontWeight: "normal",
                  fontSize: "14px",
                  padding: "10px 20px",
                  transition: "transform 0.3s ease",
                  "&:hover": {
                    backgroundColor: "primary.main",
                  },
                }}
              >
                Book Appointment
              </Button>
            </Box> */}
          </Box>
        </Box>
  );
};

export default DoctorCard;
